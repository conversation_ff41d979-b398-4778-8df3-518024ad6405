package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock;
import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.exception.LockAcquisitionException;
import com.tui.destilink.framework.locking.redis.lock.exception.LockReleaseException;
import com.tui.destilink.framework.locking.redis.lock.exception.AbstractRedisLockException;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;

/**
 * Redis-based implementation of a stamped lock providing optimistic reading,
 * read locking, and write locking capabilities.
 * <p>
 * This implementation provides a lightweight, stateless lock object with all
 * state
 * managed in Redis. It supports three types of access:
 * </p>
 * <ul>
 * <li>Optimistic read - fast, non-blocking validation</li>
 * <li>Pessimistic read - shared access with multiple readers</li>
 * <li>Write access - exclusive access</li>
 * </ul>
 * <p>
 * All reentrancy and state tracking is handled entirely by Redis using Lua
 * scripts.
 * No ThreadLocal or JVM-local state is maintained for lock tracking.
 * </p>
 * <p>
 * The stamped lock uses a sequence number (stamp) system to validate optimistic
 * reads
 * and coordinate between different lock modes, all managed in Redis.
 * </p>
 */
@Slf4j
public class RedisStampedLock extends AbstractRedisLock {
    private static final String LOCK_TYPE = "RedisStampedLock";
    private static final String STAMPED_DATA_SUFFIX = ":data";
    private static final String WRITE_MODE = "write";

    private static final String W_PREFIX = "W:";
    private static final String R_PREFIX = "R:";
    private static final String O_PREFIX = "O:";

    private final String stampedDataKey;

    /**
     * Creates a new Redis-based stamped lock with the specified key and
     * configuration from properties.
     *
     * @param redisLockOperations Redis lock operations for executing lock commands
     * @param lockOwnerSupplier   Supplier for lock owner identifiers
     * @param properties          Redis lock configuration properties
     * @param lockKey             The unique key identifying this lock in Redis
     * @param virtualThreadExecutor Virtual thread executor for async operations
     * @param watchdog            Lock watchdog for lease extension
     */
    public RedisStampedLock(
        com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations redisLockOperations,
        com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier lockOwnerSupplier,
        RedisLockProperties properties,
        String lockKey,
        java.util.concurrent.ExecutorService virtualThreadExecutor,
        com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog watchdog) {
        super(redisLockOperations, lockOwnerSupplier, properties, lockKey, virtualThreadExecutor, watchdog);
        this.stampedDataKey = lockKey + STAMPED_DATA_SUFFIX;
    }

    /**
     * Creates a new Redis-based stamped lock with the specified key and
     * configuration.
     *
     * @param redisLockOperations Redis lock operations for executing lock commands
     * @param lockOwnerSupplier   Supplier for lock owner identifiers
     * @param properties          Redis lock configuration properties
     * @param lockKey             The unique key identifying this lock in Redis
     * @param lockTtlMillis       The TTL for the lock in milliseconds
     * @param retryIntervalMillis The retry interval in milliseconds
     * @param maxRetries          Maximum number of retry attempts
     * @param virtualThreadExecutor Virtual thread executor for async operations
     * @param watchdog            Lock watchdog for lease extension
     */
    public RedisStampedLock(
            com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations redisLockOperations,
            com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey,
            long lockTtlMillis,
            long retryIntervalMillis,
            int maxRetries,
            java.util.concurrent.ExecutorService virtualThreadExecutor,
            com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog watchdog) {
        super(redisLockOperations, lockOwnerSupplier, properties, lockKey, lockTtlMillis, retryIntervalMillis, maxRetries, virtualThreadExecutor, watchdog);
        this.stampedDataKey = lockKey + STAMPED_DATA_SUFFIX;
    }

    /**
     * Gets the response cache key for this lock with a timestamp appended.
     * This is used for caching responses to idempotent operations.
     *
     * @return the response cache key with timestamp
     */
    /**
     * Creates a timestamped response cache key for this lock.
     * This is used for caching responses to idempotent operations.
     *
     * @return the response cache key with timestamp
     */
    private String createTimestampedResponseCacheKey() {
        return getResponseCacheKey() + ":" + System.nanoTime();
    }

    @Override
    protected String getLockType() {
        return LOCK_TYPE;
    }

    @Override
    protected CompletableFuture<Boolean> doTryLock(String ownerId, Duration effectiveTimeout) {
        // For standard Lock interface, we'll use write lock semantics
        return tryWriteLockAsync()
                .thenApply(stamp -> stamp != null);
    }

    // Method removed - was causing compilation errors

    @Override
    protected CompletableFuture<Void> doUnlock(String ownerId) {
        // This method is tricky with stamped lock as we don't know the stamp.
        // The concept of a generic unlock without a stamp is problematic for StampedLock.
        // We will attempt a write unlock, as that's the behavior of doLock/doTryLock.
        return unlockWriteInternalAsync(ownerId)
            .thenAccept(result -> {
                if (result == null) {
                    throw new LockReleaseException(lockKey, getLockType(), ownerId, "Failed to unlock write lock, no result from Redis.");
                }
            });
    }

    // Using default implementation from AbstractRedisLock for
    // lockInterruptiblyAsync and isLockedAsync

    // StampedLock-specific methods

    /**
     * Attempts to acquire a write lock, returning a stamp if successful.
     *
     * @return CompletableFuture with stamp if successful, null otherwise
     */
    public CompletableFuture<String> tryWriteLockAsync() {
        String ownerId = lockOwnerSupplier.get();
        return tryWriteLockInternalAsync(ownerId)
            .thenApply(stamp -> {
                if (stamp != null) {
                    watchdog.registerLock(lockKey, ownerId, Duration.ofMillis(lockTtlMillis));
                    return W_PREFIX + stamp;
                }
                return null;
            })
            .exceptionally(ex -> {
                log.error("Error trying to acquire write lock: lockKey={}, ownerId={}", lockKey, ownerId, ex);
                return null;
            });
    }

    /**
     * Attempts to acquire a read lock, returning a stamp if successful.
     *
     * @return CompletableFuture with stamp if successful, null otherwise
     */
    public CompletableFuture<String> tryReadLockAsync() {
        String ownerId = lockOwnerSupplier.get();
        return tryReadLockInternalAsync(ownerId)
            .thenApply(stamp -> {
                if (stamp != null) {
                    watchdog.registerLock(lockKey, ownerId, Duration.ofMillis(lockTtlMillis));
                    return R_PREFIX + stamp;
                }
                return null;
            })
            .exceptionally(ex -> {
                log.error("Error trying to acquire read lock: lockKey={}, ownerId={}", lockKey, ownerId, ex);
                return null;
            });
    }

    /**
     * Returns an optimistic read stamp for validation.
     *
     * @return CompletableFuture with optimistic read stamp
     */
    public CompletableFuture<String> tryOptimisticReadAsync() {
        // Get current sequence number from Redis for optimistic read validation
        return redisLockOperations.getStateLockState(stampedDataKey)
                .thenApply(data -> {
                    if (data != null) {
                        try {
                            return O_PREFIX + Long.parseLong(data);
                        } catch (NumberFormatException e) {
                            log.error("Invalid stamped data format: {}", data, e);
                            throw new LockAcquisitionException(lockKey, getLockType(), lockOwnerSupplier.get(),
                                    "Invalid stamped data format", e);
                        }
                    }
                    return O_PREFIX + "0";
                });
    }

    /**
     * Custom exception for RedisStampedLock-specific errors.
     */
    public static class RedisStampedLockException extends AbstractRedisLockException {
        public RedisStampedLockException(String lockName, String lockType, String lockOwnerId, String message,
                Throwable cause) {
            super(lockName, lockType, lockOwnerId, null, message, cause);
        }
    }

    /**
     * Validates an optimistic read stamp.
     *
     * @param stamp The stamp to validate
     * @return CompletableFuture with true if stamp is still valid
     */
    public CompletableFuture<Boolean> validateAsync(String stamp) {
        if (stamp == null || !stamp.startsWith(O_PREFIX)) {
            return CompletableFuture.completedFuture(false);
        }
        try {
            long numericStamp = Long.parseLong(stamp.substring(O_PREFIX.length()));
            return tryOptimisticReadAsync()
                    .thenApply(currentStamp -> currentStamp.equals(O_PREFIX + numericStamp));
        } catch (NumberFormatException e) {
            return CompletableFuture.completedFuture(false);
        }
    }

    /**
     * Unlocks a lock using the provided stamp.
     *
     * @param stamp The stamp from lock acquisition
     */
    public CompletableFuture<Void> unlock(String stamp) {
        if (stamp == null) {
            return CompletableFuture.failedFuture(new IllegalArgumentException("Stamp cannot be null"));
        }

        if (stamp.startsWith(W_PREFIX)) {
            return unlockWriteAsync(stamp);
        } else if (stamp.startsWith(R_PREFIX)) {
            return unlockReadAsync(stamp);
        } else {
            return CompletableFuture.failedFuture(new IllegalArgumentException("Invalid stamp format"));
        }
    }

    /**
     * Attempts to convert a read lock to a write lock.
     *
     * @param stamp The read stamp to upgrade.
     * @return A new write stamp if successful, otherwise null.
     */
    public CompletableFuture<String> tryConvertToWriteLock(String stamp) {
        if (stamp == null || !stamp.startsWith(R_PREFIX)) {
            return CompletableFuture.failedFuture(new IllegalArgumentException("Invalid read stamp for conversion."));
        }

        // This would require a dedicated Lua script.
        // For now, we simulate by unlocking read and trying to lock write.
        // This is NOT atomic and is just a placeholder.
        log.warn("tryConvertToWriteLock is not atomic and should be used with caution.");
        return unlockReadAsync(stamp).thenCompose(v -> tryWriteLockAsync());
    }


    private CompletableFuture<Void> unlockWriteAsync(String stamp) {
        String ownerId = lockOwnerSupplier.get();
        return unlockWriteInternalAsync(ownerId)
            .thenAccept(result -> {
                if (result != null) {
                    watchdog.unregisterLock(lockKey, ownerId);
                }
            })
            .exceptionally(ex -> {
                log.error("Error unlocking write lock: lockKey={}, ownerId={}, stamp={}", lockKey, ownerId, stamp,
                    ex);
                return null;
            });
    }

    private CompletableFuture<Void> unlockReadAsync(String stamp) {
        String ownerId = lockOwnerSupplier.get();
        return unlockReadInternalAsync(ownerId)
            .thenAccept(result -> {
                if (result != null) {
                    watchdog.unregisterLock(lockKey, ownerId);
                }
            })
            .exceptionally(ex -> {
                log.error("Error unlocking read lock: lockKey={}, ownerId={}, stamp={}", lockKey, ownerId, stamp,
                    ex);
                return null;
            });
    }

    // Internal implementation methods

    private CompletableFuture<Long> tryWriteLockInternalAsync(String ownerId) {
        // Let RedisLockOperationsImpl generate UUID centrally
        return redisLockOperations.tryWriteLock(
                lockKey,
                null, // requestUuid - generated internally
                createTimestampedResponseCacheKey(),
                String.valueOf(lockTtlMillis),
                ownerId,
                stampedDataKey,
                WRITE_MODE);
    }

    private CompletableFuture<Long> tryReadLockInternalAsync(String ownerId) {
        // Let RedisLockOperationsImpl generate UUID centrally
        return redisLockOperations.tryReadLock(
                lockKey,
                createTimestampedResponseCacheKey(),
                null, // requestUuid - generated internally 
                String.valueOf(lockTtlMillis),
                ownerId,
                stampedDataKey);
    }

    private CompletableFuture<String> unlockWriteInternalAsync(String ownerId) {
        return redisLockOperations.unlockWriteLock(
                lockKey,
                ownerId,
                null, // Let stamp be generated internally if needed
                "UNLOCK")
                .thenApply(result -> result ? "OK" : "FAILED");
    }

    private CompletableFuture<String> unlockReadInternalAsync(String ownerId) {
        return redisLockOperations.unlockReadLock(
                lockKey,
                ownerId,
                null) // Let stamp be generated internally if needed
                .thenApply(result -> result ? "OK" : "FAILED");
    }

    // Method removed - was causing compilation errors

    @Override
    public boolean isReadLock() {
        return false; // RedisStampedLock is neither purely read nor write
    }

    @Override
    public boolean isWriteLock() {
        return false; // RedisStampedLock is neither purely read nor write
    }
}
