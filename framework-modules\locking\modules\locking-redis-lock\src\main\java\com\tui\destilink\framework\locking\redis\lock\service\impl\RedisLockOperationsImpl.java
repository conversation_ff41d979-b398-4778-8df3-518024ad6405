package com.tui.destilink.framework.locking.redis.lock.service.impl;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockErrorHandler;
import com.tui.destilink.framework.locking.redis.lock.service.ScriptLoader;
import com.tui.destilink.framework.locking.redis.lock.util.VirtualThreadContextUtils;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import com.tui.destilink.framework.redis.core.script.ImmutableLettuceScript;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.script.RedisScript;

import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;

/**
 * Redis-based implementation of distributed lock operations with comprehensive retry logic
 * and Virtual Thread integration for improved I/O handling and resilience.
 */
@Slf4j
public class RedisLockOperationsImpl implements RedisLockOperations {

    private final ClusterCommandExecutor clusterCommandExecutor;
    private final ScriptLoader scriptLoader;
    private final RedisLockProperties properties;
    private final ExecutorService virtualThreadExecutor;
    private final RedisLockErrorHandler errorHandler;
    private final ImmutableLettuceScript<Long> tryLockScript;
    private final ImmutableLettuceScript<Long> unlockScript;
    private final ImmutableLettuceScript<Long> extendLockScript;
    private final ImmutableLettuceScript<Long> checkLockScript;
    private final ImmutableLettuceScript<Long> watchdogRefreshLockScript;
    private final ImmutableLettuceScript<Long> acquireLockScript;
    private final ImmutableLettuceScript<Long> releaseLockScript;
    private final ImmutableLettuceScript<Long> updateStateScript;
    private final ImmutableLettuceScript<Long> updateStateIfEqualsScript;
    private final ImmutableLettuceScript<Long> tryStateLockScript;
    private final ImmutableLettuceScript<Long> unlockStateLockScript;
    private final ImmutableLettuceScript<Long> tryWriteLockScript;
    private final ImmutableLettuceScript<Long> tryReadLockScript;
    private final ImmutableLettuceScript<Long> unlockWriteLockScript;
    private final ImmutableLettuceScript<Long> unlockReadLockScript;
    
    // Simple scripts for basic Redis operations
    private final ImmutableLettuceScript<String> hgetScript;
    private final ImmutableLettuceScript<Long> pttlScript;
    private final ImmutableLettuceScript<String> getScript;

    // Note: Circuit breaker functionality was considered but not implemented
    // as resilience is handled by the redis-core module's ClusterCommandExecutor
    // and the executeWithRetry method with errorHandler.

    public RedisLockOperationsImpl(ClusterCommandExecutor clusterCommandExecutor,
                                   ScriptLoader scriptLoader,
                                   RedisLockProperties properties,
                                   ExecutorService virtualThreadExecutor,
                                   RedisLockErrorHandler errorHandler) {
        this.clusterCommandExecutor = clusterCommandExecutor;
        this.scriptLoader = scriptLoader;
        this.properties = properties;
        this.virtualThreadExecutor = virtualThreadExecutor;
        this.errorHandler = errorHandler;
        
        // Convert RedisScript to ImmutableLettuceScript for all scripts
        this.tryLockScript = ImmutableLettuceScript.of(scriptLoader.getTryLockScript());
        this.unlockScript = ImmutableLettuceScript.of(scriptLoader.getReleaseLockScript());
        this.extendLockScript = ImmutableLettuceScript.of(scriptLoader.getExtendLockScript());
        this.checkLockScript = ImmutableLettuceScript.of(scriptLoader.getCheckLockScript());
        this.watchdogRefreshLockScript = ImmutableLettuceScript.of(scriptLoader.getWatchdogRefreshLockScript());
        this.acquireLockScript = ImmutableLettuceScript.of(scriptLoader.getAcquireLockScript());
        this.releaseLockScript = ImmutableLettuceScript.of(scriptLoader.getReleaseLockScript());
        this.updateStateScript = ImmutableLettuceScript.of(scriptLoader.getUpdateStateScript());
        this.updateStateIfEqualsScript = ImmutableLettuceScript.of(scriptLoader.getUpdateStateIfEqualsScript());
        this.tryStateLockScript = ImmutableLettuceScript.of(scriptLoader.getTryStateLockScript());
        this.unlockStateLockScript = ImmutableLettuceScript.of(scriptLoader.getUnlockStateLockScript());
        
        // Convert basic Redis operation scripts with idempotency support
        this.hgetScript = ImmutableLettuceScript.of(scriptLoader.getHgetScript());
        this.pttlScript = ImmutableLettuceScript.of(scriptLoader.getPttlScript());
        this.getScript = ImmutableLettuceScript.of(scriptLoader.getGetScript());
        
        // Convert lock-specific scripts with idempotency support
        this.tryWriteLockScript = ImmutableLettuceScript.of(scriptLoader.getTryWriteLockScript());
        this.tryReadLockScript = ImmutableLettuceScript.of(scriptLoader.getTryReadLockScript());
        this.unlockWriteLockScript = ImmutableLettuceScript.of(scriptLoader.getUnlockWriteLockScript());
        this.unlockReadLockScript = ImmutableLettuceScript.of(scriptLoader.getUnlockReadLockScript());
    }

    /**
     * Generates a unique UUID for request idempotency.
     * This centralizes UUID generation for all lock operations.
     *
     * @return A unique UUID string for the current operation
     */
    private String generateRequestUuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * Gets the response cache TTL in seconds from configuration.
     *
     * @return Response cache TTL in seconds as a string
     */
    private String getResponseCacheTtlSeconds() {
        return String.valueOf(properties.getResponseCacheTtl().getSeconds());
    }

    /**
     * Executes a Redis operation with retry logic using Virtual Thread-friendly delays.
     * This method implements application-level retries for individual Redis operations
     * as specified in the detailed plan.
     *
     * @param operationName Name of the operation for logging
     * @param operation The operation to execute
     * @param <T> Return type of the operation
     * @return CompletableFuture with the operation result
     */
    private <T> CompletableFuture<T> executeWithRetry(String operationName, Supplier<CompletableFuture<T>> operation) {
        return VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
            int maxRetries = properties.getDefaults().getMaxRetries();
            long retryIntervalMillis = properties.getDefaults().getRetryInterval().toMillis();

            Exception lastException = null;

            for (int attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    return operation.get().join();
                } catch (Exception e) {
                    lastException = e;

                    // Use error handler to classify exception as retryable or not
                    boolean isRetryable = errorHandler.isRetryableException(e);

                    if (!isRetryable || attempt >= maxRetries) {
                        log.error("Operation {} failed after {} attempts (non-retryable or max retries reached)",
                                operationName, attempt + 1, e);
                        throw e;
                    }

                    log.warn("Operation {} failed on attempt {}/{}, retrying in {}ms",
                            operationName, attempt + 1, maxRetries + 1, retryIntervalMillis, e);

                    // Use Thread.sleep() for Virtual Thread-friendly delay
                    try {
                        Thread.sleep(retryIntervalMillis);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Retry interrupted", ie);
                    }
                }
            }

            // This should never be reached, but just in case
            throw new RuntimeException("Operation " + operationName + " failed after all retries", lastException);
        });
    }

    // Helper method for tryLock implementation
    private CompletableFuture<Boolean> tryLock(String lockKey, String ownerId, Duration ttl) {
        log.debug("tryLock called: key={}, value={}, ttl={}", lockKey, ownerId, ttl);

        return executeWithRetry("tryLock", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();

            // Construct keys according to new format
            String lockDataKey = lockKey + ":data";
            String responseCacheKey = lockKey + ":response_cache:" + requestUuid;

            String[] keys = new String[] { lockKey, lockDataKey, responseCacheKey };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    String.valueOf(ttl.toMillis()),
                    "reentrant" // Default lock type
            };

            return clusterCommandExecutor.executeScript(
                    lockKey,
                    tryLockScript,
                    keys,
                    args).thenApply(result -> {
                        // Parse status code (negative indicates success)
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            return statusCode < 0; // Negative indicates success
                        }
                        return false;
                    });
        });
    }

    @Override
    public CompletableFuture<Void> unlock(String lockKey, String ownerId) {
        log.debug("unlock called: key={}, value={}", lockKey, ownerId);

        return executeWithRetry("unlock", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();

            // Construct keys according to new format
            String lockDataKey = lockKey + ":data";
            String responseCacheKey = lockKey + ":response_cache:" + requestUuid;
            String unlockChannel = lockKey + ":unlock";

            String[] keys = new String[] { lockKey, lockDataKey, responseCacheKey, unlockChannel };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    "reentrant" // Default lock type
            };

            return clusterCommandExecutor.executeScript(
                    lockKey,
                    unlockScript,
                    keys,
                    args).thenAccept(result -> {
                        // Parse status code
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            if (statusCode == 0) {
                                log.warn("Lock not held by current owner: {}, {}", lockKey, ownerId);
                            } else if (statusCode == -1) {
                                log.warn("Lock held by different owner: {}, {}", lockKey, ownerId);
                            }
                        }
                    });
        });
    }

    @Override
    public CompletableFuture<Boolean> extendLock(String lockKey, String ownerId, Duration ttl) {
        log.debug("extendLock called: key={}, value={}, ttl={}", lockKey, ownerId, ttl);

        return executeWithRetry("extendLock", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();

            // Construct keys according to new format
            String lockDataKey = lockKey + ":data";
            String responseCacheKey = lockKey + ":response_cache:" + requestUuid;

            String[] keys = new String[] { lockKey, lockDataKey, responseCacheKey };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    String.valueOf(ttl.toMillis()),
                    "reentrant" // Default lock type
            };

            return clusterCommandExecutor.executeScript(
                    lockKey,
                    extendLockScript,
                    keys,
                    args).thenApply(result -> {
                        // Parse status code (positive indicates success)
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            return statusCode > 0; // Positive indicates success
                        }
                        return false;
                    });
        });
    }

    @Override
    public CompletableFuture<Boolean> isLocked(String lockKey) {
        log.debug("isLocked called: key={}", lockKey);
        
        // Use the checkLock script with a dummy owner ID
        // The script will return 0 if the lock doesn't exist
        String dummyOwnerId = "dummy-" + UUID.randomUUID().toString();
        String[] keys = new String[] { lockKey };
        String[] args = new String[] { dummyOwnerId };
        
        return executeWithRetry("isLocked", () -> {
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    checkLockScript,
                    keys,
                    args).thenApply(result -> {
                        if (result instanceof Long) {
                            // If the result is 0, the lock doesn't exist
                            // If it's -1, the lock exists but is held by someone else
                            // If it's 1, the lock exists and is held by the dummy owner (shouldn't happen)
                            Long statusCode = (Long) result;
                            return statusCode != 0; // Non-zero means the lock exists
                        }
                        return false;
                    });
        });
    }

    @Override
    public CompletableFuture<Boolean> tryLock(String lockKey, String ownerId, Duration ttl, Duration acquireTimeout) {
        log.debug("tryLock with timeout called: key={}, value={}, ttl={}, timeout={}", lockKey, ownerId, ttl,
                acquireTimeout);

        return executeWithRetry("tryLockWithTimeout", () -> {
            long startTime = System.currentTimeMillis();
            long timeoutMillis = acquireTimeout.toMillis();

            return tryLockWithRetries(lockKey, ownerId, ttl, startTime, timeoutMillis);
        });
    }

    private CompletableFuture<Boolean> tryLockWithRetries(String lockKey, String ownerId, Duration ttl,
                                                         long startTime, long timeoutMillis) {
        return tryLock(lockKey, ownerId, ttl).thenCompose(acquired -> {
            if (acquired) {
                return CompletableFuture.completedFuture(true);
            }

            long elapsedTime = System.currentTimeMillis() - startTime;
            if (elapsedTime >= timeoutMillis) {
                return CompletableFuture.completedFuture(false);
            }

            long remainingTime = timeoutMillis - elapsedTime;
            return VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
                try {
                    Thread.sleep(Math.min(remainingTime, properties.getDefaults().getRetryInterval().toMillis()));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException(e);
                }
                return tryLockWithRetries(lockKey, ownerId, ttl, startTime, timeoutMillis).join();
            });
        });
    }

    @Override
    public CompletableFuture<Long> refreshLockByWatchdog(String lockKey, String ownerId, long targetExpiresAtMillis) {
        log.debug("refreshLockByWatchdog called: key={}, owner={}, newExpiresAt={}", lockKey, ownerId, targetExpiresAtMillis);
        
        String requestUuid = generateRequestUuid();
        String responseCacheKey = lockKey + ":watchdog_refresh:" + requestUuid;
        
        String[] keys = new String[] { lockKey, responseCacheKey };
        String[] args = new String[] {
                requestUuid,
                getResponseCacheTtlSeconds(),
                ownerId,
                String.valueOf(targetExpiresAtMillis)
        };
        
        return executeWithRetry("refreshLockByWatchdog", () -> {
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    watchdogRefreshLockScript,
                    keys,
                    args).thenApply(result -> {
                        log.debug("refreshLockByWatchdog result: {}", result);
                        return result instanceof Long ? (Long) result : 0L;
                    });
        });
    }
    
    @Override
    public CompletableFuture<String> hget(String key, String field) {
        log.debug("hget called: key={}, field={}", key, field);
        
        return executeWithRetry("hget", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();
            
            // Construct the response cache key
            String responseCacheKey = key + ":hget:" + field + ":" + requestUuid;
            
            String[] keys = new String[] { key, responseCacheKey };
            String[] args = new String[] { 
                requestUuid,
                getResponseCacheTtlSeconds(),
                field 
            };
            
            return clusterCommandExecutor.executeScript(
                    key,
                    hgetScript,
                    keys,
                    args);
        });
    }
    
    @Override
    public CompletableFuture<Long> getTtl(String lockKey) {
        log.debug("getTtl called: key={}", lockKey);
        
        return executeWithRetry("getTtl", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();
            
            // Construct the response cache key
            String responseCacheKey = lockKey + ":pttl:" + requestUuid;
            
            String[] keys = new String[] { lockKey, responseCacheKey };
            String[] args = new String[] { 
                requestUuid,
                getResponseCacheTtlSeconds()
            };
            
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    pttlScript,
                    keys,
                    args);
        });
    }
    
    @Override
    public CompletableFuture<Boolean> acquireLock(String lockKey, String ownerId, Duration ttl) {
        log.debug("acquireLock called: key={}, value={}, ttl={}", lockKey, ownerId, ttl);
        
        return executeWithRetry("acquireLock", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();
            
            // Construct keys according to format
            String lockDataKey = lockKey + ":data";
            String responseCacheKey = lockKey + ":response_cache:" + requestUuid;
            
            String[] keys = new String[] { lockKey, lockDataKey, responseCacheKey };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    String.valueOf(ttl.toMillis()),
                    "reentrant" // Default lock type for basic acquire operations
            };
            
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    acquireLockScript,
                    keys,
                    args).thenApply(result -> {
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            return statusCode > 0; // Positive indicates success
                        }
                        return false;
                    });
        });
    }
    
    @Override
    public CompletableFuture<Boolean> releaseLock(String lockKey, String ownerId) {
        log.debug("releaseLock called: key={}, value={}", lockKey, ownerId);
        
        return executeWithRetry("releaseLock", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();
            
            // Construct keys according to format
            String lockDataKey = lockKey + ":data";
            String responseCacheKey = lockKey + ":response_cache:" + requestUuid;
            String unlockChannel = lockKey + ":unlock";
            
            String[] keys = new String[] { lockKey, lockDataKey, responseCacheKey, unlockChannel };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    "reentrant" // Default lock type for basic release operations
            };
            
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    releaseLockScript,
                    keys,
                    args).thenApply(result -> {
                        // Parse status code from unlock script
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            if (statusCode == 0) {
                                log.warn("Lock not held by current owner: {}, {}", lockKey, ownerId);
                                return false;
                            } else if (statusCode == -1) {
                                log.warn("Lock held by different owner: {}, {}", lockKey, ownerId);
                                return false;
                            }
                            return statusCode > 0; // Positive indicates success
                        }
                        log.warn("Unexpected response type from unlock script: {}", result);
                        return false;
                    });
        });
    }
    
    @Override
    public CompletableFuture<Boolean> checkLock(String lockKey, String ownerId) {
        log.debug("checkLock called: key={}, value={}", lockKey, ownerId);
        
        return executeWithRetry("checkLock", () -> {
            String[] keys = new String[] { lockKey };
            String[] args = new String[] { ownerId };
            
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    checkLockScript,
                    keys,
                    args).thenApply(result -> {
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            return statusCode > 0; // Positive indicates success (lock held by owner)
                        }
                        return false;
                    });
        });
    }
    
    @Override
    public CompletableFuture<String> getString(String key) {
        log.debug("getString called: key={}", key);
        
        return executeWithRetry("getString", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();
            
            // Construct the response cache key
            String responseCacheKey = key + ":get:" + requestUuid;
            
            String[] keys = new String[] { key, responseCacheKey };
            String[] args = new String[] { 
                requestUuid,
                getResponseCacheTtlSeconds()
            };
            
            return clusterCommandExecutor.executeScript(
                    key,
                    getScript,
                    keys,
                    args);
        });
    }
    
    @Override
    public CompletableFuture<Boolean> tryStateLock(String lockKey, String ownerId, String requestUuid,
                                                  String initialState, String stateKey, String lockTtl,
                                                  boolean allowExisting, String stateExpiration,
                                                  String responseCacheKey) {
        log.debug("tryStateLock called: key={}, owner={}, requestUuid={}, initialState={}, stateKey={}, lockTtl={}, allowExisting={}, stateExpiration={}",
                lockKey, ownerId, requestUuid, initialState, stateKey, lockTtl, allowExisting, stateExpiration);
        
        return executeWithRetry("tryStateLock", () -> {
            // Use the provided requestUuid for idempotency
            
            String[] keys = new String[] { lockKey, stateKey, responseCacheKey };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    initialState,
                    stateKey,
                    lockTtl,
                    allowExisting ? "1" : "0",
                    stateExpiration,
                    responseCacheKey
            };
            
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    tryStateLockScript,
                    keys,
                    args).thenApply(result -> {
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            return statusCode > 0; // Positive indicates success
                        }
                        return false;
                    });
        });
    }
    
    @Override
    public CompletableFuture<Boolean> unlockStateLock(
            String lockKey,
            String ownerId,
            String newState,
            String requestUuid,
            String stateKey,
            String stateExpiration,
            String stateExpirationMs,
            String unlockCommand,
            String responseCacheKey) {
        log.debug("unlockStateLock called: key={}, owner={}, newState={}, requestUuid={}, stateKey={}, stateExpiration={}, stateExpirationMs={}, unlockCommand={}",
                lockKey, ownerId, newState, requestUuid, stateKey, stateExpiration, stateExpirationMs, unlockCommand);
        
        return executeWithRetry("unlockStateLock", () -> {
            // Use the provided requestUuid for idempotency
            String unlockChannel = lockKey + ":unlock";
            
            String[] keys = new String[] { lockKey, stateKey, responseCacheKey, unlockChannel };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    newState,
                    stateKey,
                    stateExpiration,
                    stateExpirationMs,
                    unlockCommand,
                    responseCacheKey
            };
            
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    unlockStateLockScript,
                    keys,
                    args).thenApply(result -> {
                        // Parse status code
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            if (statusCode == 0) {
                                log.warn("State lock not held by current owner: {}, {}", lockKey, ownerId);
                                return false;
                            } else if (statusCode == -1) {
                                log.warn("State lock held by different owner: {}, {}", lockKey, ownerId);
                                return false;
                            }
                            return statusCode > 0; // Positive indicates success
                        }
                        return false;
                    });
        });
    }
    
    @Override
    public CompletableFuture<String> getStateLockState(String stateKey) {
        log.debug("getStateLockState called: stateKey={}", stateKey);
        
        return executeWithRetry("getStateLockState", () -> {
            String[] keys = new String[] { stateKey };
            String[] args = new String[] {};
            
            return clusterCommandExecutor.executeScript(
                    stateKey,
                    getScript,
                    keys,
                    args);
        });
    }
    
    @Override
    public CompletableFuture<Boolean> updateState(
            String lockKey,
            String ownerId,
            String requestUuid,
            String newState,
            String stateKey,
            String stateExpiration,
            String stateExpirationMs,
            String responseCacheKey) {
        log.debug("updateState called: lockKey={}, ownerId={}, requestUuid={}, newState={}, stateKey={}, stateExpiration={}, stateExpirationMs={}",
                lockKey, ownerId, requestUuid, newState, stateKey, stateExpiration, stateExpirationMs);
        
        return executeWithRetry("updateState", () -> {
            // Use the provided requestUuid for idempotency
            
            String[] keys = new String[] { lockKey, stateKey, responseCacheKey };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    newState,
                    stateKey,
                    stateExpiration,
                    stateExpirationMs,
                    responseCacheKey
            };
            
            return clusterCommandExecutor.executeScript(
                    stateKey,
                    updateStateScript,
                    keys,
                    args).thenApply(result -> {
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            return statusCode > 0; // Positive indicates success
                        }
                        return false;
                    });
        });
    }
    
    @Override
    public CompletableFuture<Boolean> updateStateIfEquals(
            String lockKey,
            String ownerId,
            String requestUuid,
            String expectedState,
            String newState,
            String stateKey,
            String stateExpiration,
            String stateExpirationMs,
            String responseCacheKey) {
        log.debug("updateStateIfEquals called: lockKey={}, ownerId={}, requestUuid={}, expectedState={}, newState={}, stateKey={}, stateExpiration={}, stateExpirationMs={}",
                lockKey, ownerId, requestUuid, expectedState, newState, stateKey, stateExpiration, stateExpirationMs);
        
        return executeWithRetry("updateStateIfEquals", () -> {
            // Use the provided requestUuid for idempotency
            
            String[] keys = new String[] { lockKey, stateKey, responseCacheKey };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    expectedState,
                    newState,
                    stateKey,
                    stateExpiration,
                    stateExpirationMs,
                    responseCacheKey
            };
            
            return clusterCommandExecutor.executeScript(
                    stateKey,
                    updateStateIfEqualsScript,
                    keys,
                    args).thenApply(result -> {
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            return statusCode > 0; // Positive indicates success
                        }
                        return false;
                    });
        });
    }
    
    @Override
    public CompletableFuture<Long> tryWriteLock(
            String lockKey,
            String requestUuid,
            String responseCacheKey,
            String lockTtl,
            String ownerId,
            String stampedDataKey,
            String stampedLockMode) {
        log.debug("tryWriteLock called: lockKey={}, requestUuid={}, responseCacheKey={}, lockTtl={}, ownerId={}, stampedDataKey={}, stampedLockMode={}",
                lockKey, requestUuid, responseCacheKey, lockTtl, ownerId, stampedDataKey, stampedLockMode);
        
        return executeWithRetry("tryWriteLock", () -> {
            // If no requestUuid is provided, generate one
            String reqUuid = requestUuid != null && !requestUuid.isEmpty() ? requestUuid : generateRequestUuid();
            
            // If no responseCacheKey is provided, generate one
            String respCacheKey = responseCacheKey != null && !responseCacheKey.isEmpty() ? 
                    responseCacheKey : lockKey + ":tryWriteLock:" + reqUuid;
                    
            String[] keys = new String[] { lockKey, respCacheKey, stampedDataKey };
            String[] args = new String[] {
                    reqUuid,
                    getResponseCacheTtlSeconds(),
                    lockTtl,
                    ownerId,
                    stampedLockMode
            };
            
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    tryWriteLockScript,
                    keys,
                    args).thenApply(result -> {
                        if (result instanceof Long) {
                            return (Long) result;
                        }
                        return 0L;
                    });
        });
    }
    
    @Override
    public CompletableFuture<Long> tryReadLock(
            String lockKey,
            String requestUuid,
            String responseCacheKey,
            String lockTtl,
            String ownerId,
            String stampedDataKey) {
        log.debug("tryReadLock called: lockKey={}, requestUuid={}, responseCacheKey={}, lockTtl={}, ownerId={}, stampedDataKey={}",
                lockKey, requestUuid, responseCacheKey, lockTtl, ownerId, stampedDataKey);
        
        return executeWithRetry("tryReadLock", () -> {
            // If no requestUuid is provided, generate one
            String reqUuid = requestUuid != null && !requestUuid.isEmpty() ? requestUuid : generateRequestUuid();
            
            // If no responseCacheKey is provided, generate one
            String respCacheKey = responseCacheKey != null && !responseCacheKey.isEmpty() ? 
                    responseCacheKey : lockKey + ":tryReadLock:" + reqUuid;
                    
            String[] keys = new String[] { lockKey, respCacheKey, stampedDataKey };
            String[] args = new String[] {
                    reqUuid,
                    getResponseCacheTtlSeconds(),
                    lockTtl,
                    ownerId
            };
            
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    tryReadLockScript,
                    keys,
                    args).thenApply(result -> {
                        if (result instanceof Long) {
                            return (Long) result;
                        }
                        return 0L;
                    });
        });
    }
    
    @Override
    public CompletableFuture<Boolean> unlockWriteLock(
            String lockKey,
            String ownerId,
            String stamp,
            String unlockCommand) {
        log.debug("unlockWriteLock called: lockKey={}, ownerId={}, stamp={}, unlockCommand={}",
                lockKey, ownerId, stamp, unlockCommand);
        
        return executeWithRetry("unlockWriteLock", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();
            
            // Create stampedDataKey (same format as in tryWriteLock)
            String stampedDataKey = lockKey + ":data";
            
            // Construct the response cache key
            String responseCacheKey = lockKey + ":unlockWrite:" + requestUuid;
            
            String[] keys = new String[] { lockKey, stampedDataKey, responseCacheKey };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    stamp != null ? stamp : "",
                    unlockCommand != null ? unlockCommand : "DELETE"
            };
            
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    unlockWriteLockScript,
                    keys,
                    args).thenApply(result -> {
                        // Parse status code
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            if (statusCode == 0) {
                                log.warn("Write lock not held by current owner: {}, {}", lockKey, ownerId);
                                return false;
                            }
                            return statusCode > 0; // Positive indicates success
                        }
                        return false;
                    });
        });
    }
    
    @Override
    public CompletableFuture<Boolean> unlockReadLock(String lockKey, String ownerId, String stamp) {
        log.debug("unlockReadLock called: lockKey={}, ownerId={}, stamp={}",
                lockKey, ownerId, stamp);
        
        return executeWithRetry("unlockReadLock", () -> {
            // Generate requestUuid centrally for idempotency
            String requestUuid = generateRequestUuid();
            
            // Create stampedDataKey (same format as in tryReadLock)
            String stampedDataKey = lockKey + ":data";
            
            // Construct the response cache key
            String responseCacheKey = lockKey + ":unlockRead:" + requestUuid;
            
            String[] keys = new String[] { lockKey, stampedDataKey, responseCacheKey };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    stamp != null ? stamp : ""
            };
            
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    unlockReadLockScript,
                    keys,
                    args).thenApply(result -> {
                        // Parse status code
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            if (statusCode == 0) {
                                log.warn("Read lock not held by current owner: {}, {}", lockKey, ownerId);
                                return false;
                            }
                            return statusCode > 0; // Positive indicates success
                        }
                        return false;
                    });
        });
    }
}