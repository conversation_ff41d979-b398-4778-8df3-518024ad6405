package com.tui.destilink.framework.locking.redis.lock.service.impl;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockErrorHandler;
import com.tui.destilink.framework.locking.redis.lock.service.ScriptLoader;
import com.tui.destilink.framework.locking.redis.lock.util.VirtualThreadContextUtils;
import com.tui.destilink.framework.locking.redis.lock.util.RedisKeyComposer;
import com.tui.destilink.framework.locking.redis.lock.util.RedisLockOperationsHelper;
import com.tui.destilink.framework.locking.redis.lock.util.RedisLockScriptExecutor;
import com.tui.destilink.framework.redis.core.cluster.ClusterCommandExecutor;
import com.tui.destilink.framework.redis.core.script.ImmutableLettuceScript;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.script.RedisScript;

import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;

/**
 * Redis-based implementation of distributed lock operations with comprehensive retry logic
 * and Virtual Thread integration for improved I/O handling and resilience.
 */
@Slf4j
public class RedisLockOperationsImpl implements RedisLockOperations {

    private final ClusterCommandExecutor clusterCommandExecutor;
    private final ScriptLoader scriptLoader;
    private final RedisLockProperties properties;
    private final ExecutorService virtualThreadExecutor;
    private final RedisLockErrorHandler errorHandler;
    private final RedisLockOperationsHelper helper;
    private final RedisLockScriptExecutor scriptExecutor;
    private final ImmutableLettuceScript<Long> tryLockScript;
    private final ImmutableLettuceScript<Long> unlockScript;
    private final ImmutableLettuceScript<Long> extendLockScript;
    private final ImmutableLettuceScript<Long> checkLockScript;
    private final ImmutableLettuceScript<Long> watchdogRefreshLockScript;
    private final ImmutableLettuceScript<Long> acquireLockScript;
    private final ImmutableLettuceScript<Long> releaseLockScript;
    private final ImmutableLettuceScript<Long> updateStateScript;
    private final ImmutableLettuceScript<Long> updateStateIfEqualsScript;
    private final ImmutableLettuceScript<Long> tryStateLockScript;
    private final ImmutableLettuceScript<Long> unlockStateLockScript;
    private final ImmutableLettuceScript<Long> tryWriteLockScript;
    private final ImmutableLettuceScript<Long> tryReadLockScript;
    private final ImmutableLettuceScript<Long> unlockWriteLockScript;
    private final ImmutableLettuceScript<Long> unlockReadLockScript;
    
    // Simple scripts for basic Redis operations
    private final ImmutableLettuceScript<String> hgetScript;
    private final ImmutableLettuceScript<Long> pttlScript;
    private final ImmutableLettuceScript<String> getScript;

    // Note: Circuit breaker functionality was considered but not implemented
    // as resilience is handled by the redis-core module's ClusterCommandExecutor
    // and the executeWithRetry method with errorHandler.

    public RedisLockOperationsImpl(ClusterCommandExecutor clusterCommandExecutor,
                                   ScriptLoader scriptLoader,
                                   RedisLockProperties properties,
                                   ExecutorService virtualThreadExecutor,
                                   RedisLockErrorHandler errorHandler) {
        this.clusterCommandExecutor = clusterCommandExecutor;
        this.scriptLoader = scriptLoader;
        this.properties = properties;
        this.virtualThreadExecutor = virtualThreadExecutor;
        this.errorHandler = errorHandler;
        this.helper = new RedisLockOperationsHelper(clusterCommandExecutor, properties);
        this.scriptExecutor = new RedisLockScriptExecutor(clusterCommandExecutor, properties,
                virtualThreadExecutor, errorHandler, helper);
        
        // Convert RedisScript to ImmutableLettuceScript for all scripts
        this.tryLockScript = ImmutableLettuceScript.of(scriptLoader.getTryLockScript());
        this.unlockScript = ImmutableLettuceScript.of(scriptLoader.getReleaseLockScript());
        this.extendLockScript = ImmutableLettuceScript.of(scriptLoader.getExtendLockScript());
        this.checkLockScript = ImmutableLettuceScript.of(scriptLoader.getCheckLockScript());
        this.watchdogRefreshLockScript = ImmutableLettuceScript.of(scriptLoader.getWatchdogRefreshLockScript());
        this.acquireLockScript = ImmutableLettuceScript.of(scriptLoader.getAcquireLockScript());
        this.releaseLockScript = ImmutableLettuceScript.of(scriptLoader.getReleaseLockScript());
        this.updateStateScript = ImmutableLettuceScript.of(scriptLoader.getUpdateStateScript());
        this.updateStateIfEqualsScript = ImmutableLettuceScript.of(scriptLoader.getUpdateStateIfEqualsScript());
        this.tryStateLockScript = ImmutableLettuceScript.of(scriptLoader.getTryStateLockScript());
        this.unlockStateLockScript = ImmutableLettuceScript.of(scriptLoader.getUnlockStateLockScript());
        
        // Convert basic Redis operation scripts with idempotency support
        this.hgetScript = ImmutableLettuceScript.of(scriptLoader.getHgetScript());
        this.pttlScript = ImmutableLettuceScript.of(scriptLoader.getPttlScript());
        this.getScript = ImmutableLettuceScript.of(scriptLoader.getGetScript());
        
        // Convert lock-specific scripts with idempotency support
        this.tryWriteLockScript = ImmutableLettuceScript.of(scriptLoader.getTryWriteLockScript());
        this.tryReadLockScript = ImmutableLettuceScript.of(scriptLoader.getTryReadLockScript());
        this.unlockWriteLockScript = ImmutableLettuceScript.of(scriptLoader.getUnlockWriteLockScript());
        this.unlockReadLockScript = ImmutableLettuceScript.of(scriptLoader.getUnlockReadLockScript());
    }

    /**
     * Generates a unique UUID for request idempotency.
     * This centralizes UUID generation for all lock operations.
     *
     * @return A unique UUID string for the current operation
     */
    private String generateRequestUuid() {
        return helper.generateRequestUuid();
    }

    /**
     * Gets the response cache TTL in seconds from configuration.
     *
     * @return Response cache TTL in seconds as a string
     */
    private String getResponseCacheTtlSeconds() {
        return helper.getResponseCacheTtlSeconds();
    }

    // executeWithRetry method removed - now handled by RedisLockScriptExecutor

    // Helper method for tryLock implementation
    private CompletableFuture<Boolean> tryLock(String lockKey, String ownerId, Duration ttl) {
        log.debug("tryLock called: key={}, value={}, ttl={}", lockKey, ownerId, ttl);
        return scriptExecutor.executeBasicLockScript(lockKey, ownerId, ttl, tryLockScript);
    }

    @Override
    public CompletableFuture<Void> unlock(String lockKey, String ownerId) {
        log.debug("unlock called: key={}, value={}", lockKey, ownerId);
        return scriptExecutor.executeUnlockScript(lockKey, ownerId, unlockScript);
    }

    @Override
    public CompletableFuture<Boolean> extendLock(String lockKey, String ownerId, Duration ttl) {
        log.debug("extendLock called: key={}, value={}, ttl={}", lockKey, ownerId, ttl);
        return scriptExecutor.executeBasicLockScript(lockKey, ownerId, ttl, extendLockScript);
    }

    @Override
    public CompletableFuture<Boolean> isLocked(String lockKey) {
        log.debug("isLocked called: key={}", lockKey);
        return scriptExecutor.executeSimpleOperationScript(lockKey, "isLocked", checkLockScript)
                .thenApply(result -> {
                    if (result instanceof Long) {
                        Long statusCode = (Long) result;
                        return statusCode != 0; // Non-zero means the lock exists
                    }
                    return false;
                });
    }

    @Override
    public CompletableFuture<Boolean> tryLock(String lockKey, String ownerId, Duration ttl, Duration acquireTimeout) {
        log.debug("tryLock with timeout called: key={}, value={}, ttl={}, timeout={}", lockKey, ownerId, ttl,
                acquireTimeout);
        // For timeout-based tryLock, we delegate to the basic tryLock for now
        // A more sophisticated implementation could be added to the script executor if needed
        return tryLock(lockKey, ownerId, ttl);
    }

    @Override
    public CompletableFuture<Long> refreshLockByWatchdog(String lockKey, String ownerId, long targetExpiresAtMillis) {
        log.debug("refreshLockByWatchdog called: key={}, owner={}, newExpiresAt={}", lockKey, ownerId, targetExpiresAtMillis);
        return scriptExecutor.executeSimpleOperationScript(lockKey, "watchdog_refresh", watchdogRefreshLockScript);
    }
    
    @Override
    public CompletableFuture<String> hget(String key, String field) {
        log.debug("hget called: key={}, field={}", key, field);
        return scriptExecutor.executeHashFieldOperationScript(key, field, "hget", hgetScript);
    }
    
    @Override
    public CompletableFuture<Long> getTtl(String lockKey) {
        log.debug("getTtl called: key={}", lockKey);
        return scriptExecutor.executeSimpleOperationScript(lockKey, "pttl", pttlScript);
    }
    
    @Override
    public CompletableFuture<Boolean> acquireLock(String lockKey, String ownerId, Duration ttl) {
        log.debug("acquireLock called: key={}, value={}, ttl={}", lockKey, ownerId, ttl);
        return scriptExecutor.executeBasicLockScript(lockKey, ownerId, ttl, acquireLockScript);
    }
    
    @Override
    public CompletableFuture<Boolean> releaseLock(String lockKey, String ownerId) {
        log.debug("releaseLock called: key={}, value={}", lockKey, ownerId);
        return scriptExecutor.executeBasicLockScript(lockKey, ownerId, Duration.ZERO, releaseLockScript);
    }

    @Override
    public CompletableFuture<Boolean> checkLock(String lockKey, String ownerId) {
        log.debug("checkLock called: key={}, value={}", lockKey, ownerId);
        return scriptExecutor.executeSimpleOperationScript(lockKey, "checkLock", checkLockScript)
                .thenApply(result -> {
                    if (result instanceof Long) {
                        Long statusCode = (Long) result;
                        return statusCode > 0; // Positive indicates success (lock held by owner)
                    }
                    return false;
                });
    }

    @Override
    public CompletableFuture<String> getString(String key) {
        log.debug("getString called: key={}", key);
        return scriptExecutor.executeSimpleOperationScript(key, "get", getScript);
    }
    
    @Override
    public CompletableFuture<Boolean> tryStateLock(String lockKey, String ownerId, String requestUuid,
                                                  String initialState, String stateKey, String lockTtl,
                                                  boolean allowExisting, String stateExpiration,
                                                  String responseCacheKey) {
        log.debug("tryStateLock called: key={}, owner={}, requestUuid={}, initialState={}, stateKey={}, lockTtl={}, allowExisting={}, stateExpiration={}",
                lockKey, ownerId, requestUuid, initialState, stateKey, lockTtl, allowExisting, stateExpiration);

        Duration ttl = Duration.ofMillis(Long.parseLong(lockTtl));
        return scriptExecutor.executeStateLockScript(lockKey, ownerId, initialState, ttl, tryStateLockScript);
    }
    
    @Override
    public CompletableFuture<Boolean> unlockStateLock(
            String lockKey,
            String ownerId,
            String newState,
            String requestUuid,
            String stateKey,
            String stateExpiration,
            String stateExpirationMs,
            String unlockCommand,
            String responseCacheKey) {
        log.debug("unlockStateLock called: key={}, owner={}, newState={}, requestUuid={}, stateKey={}, stateExpiration={}, stateExpirationMs={}, unlockCommand={}",
                lockKey, ownerId, newState, requestUuid, stateKey, stateExpiration, stateExpirationMs, unlockCommand);
        
        return executeWithRetry("unlockStateLock", () -> {
            // Use the provided requestUuid for idempotency
            String unlockChannel = lockKey + ":unlock";
            
            String[] keys = new String[] { lockKey, stateKey, responseCacheKey, unlockChannel };
            String[] args = new String[] {
                    requestUuid,
                    getResponseCacheTtlSeconds(),
                    ownerId,
                    newState,
                    stateKey,
                    stateExpiration,
                    stateExpirationMs,
                    unlockCommand,
                    responseCacheKey
            };
            
            return clusterCommandExecutor.executeScript(
                    lockKey,
                    unlockStateLockScript,
                    keys,
                    args).thenApply(result -> {
                        // Parse status code
                        if (result instanceof Long) {
                            Long statusCode = (Long) result;
                            if (statusCode == 0) {
                                log.warn("State lock not held by current owner: {}, {}", lockKey, ownerId);
                                return false;
                            } else if (statusCode == -1) {
                                log.warn("State lock held by different owner: {}, {}", lockKey, ownerId);
                                return false;
                            }
                            return statusCode > 0; // Positive indicates success
                        }
                        return false;
                    });
        });
    }
    
    @Override
    public CompletableFuture<String> getStateLockState(String stateKey) {
        log.debug("getStateLockState called: stateKey={}", stateKey);
        return scriptExecutor.executeSimpleOperationScript(stateKey, "getState", getScript);
    }
    
    @Override
    public CompletableFuture<Boolean> updateState(
            String lockKey,
            String ownerId,
            String requestUuid,
            String newState,
            String stateKey,
            String stateExpiration,
            String stateExpirationMs,
            String responseCacheKey) {
        log.debug("updateState called: lockKey={}, ownerId={}, requestUuid={}, newState={}, stateKey={}, stateExpiration={}, stateExpirationMs={}",
                lockKey, ownerId, requestUuid, newState, stateKey, stateExpiration, stateExpirationMs);

        Duration ttl = Duration.ofMillis(Long.parseLong(stateExpirationMs));
        return scriptExecutor.executeStateLockScript(lockKey, ownerId, newState, ttl, updateStateScript);
    }

    @Override
    public CompletableFuture<Boolean> updateStateIfEquals(
            String lockKey,
            String ownerId,
            String requestUuid,
            String expectedState,
            String newState,
            String stateKey,
            String stateExpiration,
            String stateExpirationMs,
            String responseCacheKey) {
        log.debug("updateStateIfEquals called: lockKey={}, ownerId={}, requestUuid={}, expectedState={}, newState={}, stateKey={}, stateExpiration={}, stateExpirationMs={}",
                lockKey, ownerId, requestUuid, expectedState, newState, stateKey, stateExpiration, stateExpirationMs);

        Duration ttl = Duration.ofMillis(Long.parseLong(stateExpirationMs));
        return scriptExecutor.executeStateLockScript(lockKey, ownerId, newState, ttl, updateStateIfEqualsScript);
    }
    
    @Override
    public CompletableFuture<Long> tryWriteLock(
            String lockKey,
            String requestUuid,
            String responseCacheKey,
            String lockTtl,
            String ownerId,
            String stampedDataKey,
            String stampedLockMode) {
        log.debug("tryWriteLock called: lockKey={}, requestUuid={}, responseCacheKey={}, lockTtl={}, ownerId={}, stampedDataKey={}, stampedLockMode={}",
                lockKey, requestUuid, responseCacheKey, lockTtl, ownerId, stampedDataKey, stampedLockMode);

        Duration ttl = Duration.ofMillis(Long.parseLong(lockTtl));
        return scriptExecutor.executeReadWriteLockScript(lockKey, ownerId, ttl, ownerId, tryWriteLockScript);
    }

    @Override
    public CompletableFuture<Long> tryReadLock(
            String lockKey,
            String requestUuid,
            String responseCacheKey,
            String lockTtl,
            String ownerId,
            String stampedDataKey) {
        log.debug("tryReadLock called: lockKey={}, requestUuid={}, responseCacheKey={}, lockTtl={}, ownerId={}, stampedDataKey={}",
                lockKey, requestUuid, responseCacheKey, lockTtl, ownerId, stampedDataKey);

        Duration ttl = Duration.ofMillis(Long.parseLong(lockTtl));
        return scriptExecutor.executeReadWriteLockScript(lockKey, ownerId, ttl, ownerId, tryReadLockScript);
    }
    
    @Override
    public CompletableFuture<Boolean> unlockWriteLock(
            String lockKey,
            String ownerId,
            String stamp,
            String unlockCommand) {
        log.debug("unlockWriteLock called: lockKey={}, ownerId={}, stamp={}, unlockCommand={}",
                lockKey, ownerId, stamp, unlockCommand);
        
        return scriptExecutor.executeBasicLockScript(lockKey, ownerId, Duration.ZERO, unlockWriteLockScript);
    }
    
    @Override
    public CompletableFuture<Boolean> unlockReadLock(String lockKey, String ownerId, String stamp) {
        log.debug("unlockReadLock called: lockKey={}, ownerId={}, stamp={}",
                lockKey, ownerId, stamp);
        
        return scriptExecutor.executeBasicLockScript(lockKey, ownerId, Duration.ZERO, unlockReadLockScript);
    }
}