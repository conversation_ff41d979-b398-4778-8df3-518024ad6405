package com.tui.destilink.framework.locking.redis.lock.test;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockErrorHandler;
import io.lettuce.core.SetArgs;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static com.tui.destilink.framework.locking.redis.lock.test.TestRedisLockValidationUtils.*;

/**
 * Real Redis-based implementation of RedisLockOperations for testing.
 * This implementation uses an actual Redis connection instead of mocks.
 */
@Slf4j
public class TestRedisLockOperationsImpl implements RedisLockOperations {

    private final StatefulRedisClusterConnection<String, String> connection;
    private final RedisLockProperties properties;
    private final RedisLockErrorHandler errorHandler;

    /**
     * Creates a new TestRedisLockOperationsImpl.
     *
     * @param connection The Redis cluster connection
     * @param properties The Redis lock properties
     * @param errorHandler The error handler for Redis operations
     */
    public TestRedisLockOperationsImpl(
            StatefulRedisClusterConnection<String, String> connection,
            RedisLockProperties properties,
            RedisLockErrorHandler errorHandler) {
        this.connection = connection;
        this.properties = properties;
        this.errorHandler = errorHandler;
    }

    @Override
    public CompletableFuture<Boolean> tryLock(String lockKey, String ownerId, Duration ttl, Duration acquireTimeout) {
        // Validate parameters
        try {
            validateLockParameters(lockKey, ownerId, ttl);
        } catch (IllegalArgumentException e) {
            return CompletableFuture.failedFuture(e);
        }

        // For simplicity in tests, we'll just do a single attempt without timeout handling
        return acquireLock(lockKey, ownerId, ttl);
    }

    @Override
    public CompletableFuture<Void> unlock(String lockKey, String ownerId) {
        // Validate parameters
        try {
            validateLockParameters(lockKey, ownerId);
        } catch (IllegalArgumentException e) {
            return CompletableFuture.failedFuture(e);
        }

        return CompletableFuture.runAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                String currentOwner = commands.get(lockKey);
                
                if (currentOwner == null) {
                    // Lock doesn't exist or is expired - already unlocked
                    return;
                } else if (currentOwner.equals(ownerId)) {
                    // Lock is held by the requesting owner - unlock it
                    commands.del(lockKey);
                } else {
                    // Lock is held by a different owner - cannot unlock
                    throw new RuntimeException("Lock held by different owner");
                }
            } catch (Exception e) {
                log.error("Error unlocking lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to unlock: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> extendLock(String lockKey, String ownerId, Duration ttl) {
        // Validate parameters
        try {
            validateLockParameters(lockKey, ownerId, ttl);
        } catch (IllegalArgumentException e) {
            return CompletableFuture.failedFuture(e);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                String currentOwner = commands.get(lockKey);
                
                if (currentOwner == null) {
                    // Lock doesn't exist or is expired - cannot extend
                    return false;
                } else if (currentOwner.equals(ownerId)) {
                    // Lock is held by the requesting owner - extend it
                    commands.pexpire(lockKey, ttl.toMillis());
                    return true;
                } else {
                    // Lock is held by a different owner - cannot extend
                    return false;
                }
            } catch (Exception e) {
                log.error("Error extending lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to extend lock: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> isLocked(String lockKey) {
        // Validate parameters
        if (lockKey == null) {
            return CompletableFuture.failedFuture(new IllegalArgumentException("lockKey cannot be null"));
        }
        if (lockKey.trim().isEmpty()) {
            return CompletableFuture.failedFuture(new IllegalArgumentException("lockKey cannot be empty"));
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                String value = commands.get(lockKey);
                return value != null;
            } catch (Exception e) {
                log.error("Error checking lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to check lock: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Long> refreshLockByWatchdog(String lockKey, String ownerId, long targetExpiresAtMillis) {
        // Validate parameters
        try {
            validateLockParameters(lockKey, ownerId);
        } catch (IllegalArgumentException e) {
            return CompletableFuture.failedFuture(e);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                String currentOwner = commands.get(lockKey);
                
                if (currentOwner == null) {
                    // Lock doesn't exist or is expired
                    return 0L;
                } else if (currentOwner.equals(ownerId)) {
                    // Lock is held by the requesting owner - refresh it
                    long currentTimeMillis = System.currentTimeMillis();
                    long ttlMillis = targetExpiresAtMillis - currentTimeMillis;
                    if (ttlMillis > 0) {
                        commands.pexpire(lockKey, ttlMillis);
                        return 1L;
                    }
                    return 0L;
                } else {
                    // Lock is held by a different owner
                    return -1L;
                }
            } catch (Exception e) {
                log.error("Error refreshing lock by watchdog: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to refresh lock by watchdog: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<String> hget(String key, String field) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                return commands.hget(key, field);
            } catch (Exception e) {
                log.error("Error getting hash field: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to get hash field: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Long> getTtl(String lockKey) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                return commands.pttl(lockKey);
            } catch (Exception e) {
                log.error("Error getting TTL: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to get TTL: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> acquireLock(String lockKey, String ownerId, Duration ttl) {
        // Validate parameters
        try {
            validateLockParameters(lockKey, ownerId, ttl);
        } catch (IllegalArgumentException e) {
            return CompletableFuture.failedFuture(e);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                SetArgs setArgs = SetArgs.Builder.nx().px(ttl.toMillis());
                String result = commands.set(lockKey, ownerId, setArgs);
                return "OK".equals(result);
            } catch (Exception e) {
                log.error("Error acquiring lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to acquire lock: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> releaseLock(String lockKey, String ownerId) {
        // Validate parameters
        try {
            validateLockParameters(lockKey, ownerId);
        } catch (IllegalArgumentException e) {
            return CompletableFuture.failedFuture(e);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                String currentOwner = commands.get(lockKey);
                
                if (currentOwner == null) {
                    // Lock doesn't exist or is expired - already unlocked
                    return false;
                } else if (currentOwner.equals(ownerId)) {
                    // Lock is held by the requesting owner - unlock it
                    commands.del(lockKey);
                    return true;
                } else {
                    // Lock is held by a different owner - cannot unlock
                    return false;
                }
            } catch (Exception e) {
                log.error("Error releasing lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to release lock: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> checkLock(String lockKey, String ownerId) {
        // Validate parameters
        try {
            validateLockParameters(lockKey, ownerId);
        } catch (IllegalArgumentException e) {
            return CompletableFuture.failedFuture(e);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                String currentOwner = commands.get(lockKey);
                return currentOwner != null && currentOwner.equals(ownerId);
            } catch (Exception e) {
                log.error("Error checking lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to check lock: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<String> getString(String key) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                return commands.get(key);
            } catch (Exception e) {
                log.error("Error getting string: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to get string: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> updateState(String lockKey, String ownerId, String requestUuid, String newState, String stateKey, String stateExpiration, String stateExpirationMs, String responseCacheKey) {
        // Validate parameters
        try {
            validateLockParameters(lockKey, ownerId);
        } catch (IllegalArgumentException e) {
            return CompletableFuture.failedFuture(e);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                
                // Check if lock exists and is owned by the requester
                String currentOwner = commands.get(lockKey);
                if (currentOwner == null || !currentOwner.equals(ownerId)) {
                    return false;
                }
                
                // Update state
                commands.set(stateKey, newState);
                
                // Set expiration if provided
                if (stateExpirationMs != null && !stateExpirationMs.isEmpty()) {
                    try {
                        long expirationMs = Long.parseLong(stateExpirationMs);
                        if (expirationMs > 0) {
                            commands.pexpire(stateKey, expirationMs);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("Invalid state expiration value: {}", stateExpirationMs);
                    }
                }
                
                return true;
            } catch (Exception e) {
                log.error("Error updating state: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to update state: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> updateStateIfEquals(String lockKey, String ownerId, String requestUuid, String expectedState, String newState, String stateKey, String stateExpiration, String stateExpirationMs, String responseCacheKey) {
        // Validate parameters
        try {
            validateLockParameters(lockKey, ownerId);
        } catch (IllegalArgumentException e) {
            return CompletableFuture.failedFuture(e);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                
                // Check if lock exists and is owned by the requester
                String currentOwner = commands.get(lockKey);
                if (currentOwner == null || !currentOwner.equals(ownerId)) {
                    return false;
                }
                
                // Check if current state matches expected state
                String currentState = commands.get(stateKey);
                if ((currentState == null && expectedState == null) || 
                    (currentState != null && currentState.equals(expectedState))) {
                    
                    // Update state
                    commands.set(stateKey, newState);
                    
                    // Set expiration if provided
                    if (stateExpirationMs != null && !stateExpirationMs.isEmpty()) {
                        try {
                            long expirationMs = Long.parseLong(stateExpirationMs);
                            if (expirationMs > 0) {
                                commands.pexpire(stateKey, expirationMs);
                            }
                        } catch (NumberFormatException e) {
                            log.warn("Invalid state expiration value: {}", stateExpirationMs);
                        }
                    }
                    
                    return true;
                }
                
                return false;
            } catch (Exception e) {
                log.error("Error updating state if equals: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to update state if equals: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> tryStateLock(String lockKey, String responseCacheKey, String requestUuid, String initialState, String stateKey, String lockTtl, boolean allowExisting, String stateExpiration, String responseCacheTtl) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                
                // Parse TTL
                long ttlMs;
                try {
                    ttlMs = Long.parseLong(lockTtl);
                    if (ttlMs <= 0) {
                        return false;
                    }
                } catch (NumberFormatException e) {
                    log.error("Invalid TTL value: {}", lockTtl);
                    return false;
                }
                
                // Try to acquire the lock
                SetArgs setArgs = SetArgs.Builder.px(ttlMs);
                if (!allowExisting) {
                    setArgs.nx();
                }
                
                String result = commands.set(lockKey, requestUuid != null ? requestUuid : UUID.randomUUID().toString(), setArgs);
                
                if ("OK".equals(result)) {
                    // Lock acquired, set initial state if provided
                    if (initialState != null && stateKey != null) {
                        commands.set(stateKey, initialState);
                        
                        // Set expiration for state if provided
                        if (stateExpiration != null && !stateExpiration.isEmpty()) {
                            try {
                                long stateExpirationMs = Long.parseLong(stateExpiration);
                                if (stateExpirationMs > 0) {
                                    commands.pexpire(stateKey, stateExpirationMs);
                                }
                            } catch (NumberFormatException e) {
                                log.warn("Invalid state expiration value: {}", stateExpiration);
                            }
                        }
                    }
                    
                    return true;
                }
                
                return false;
            } catch (Exception e) {
                log.error("Error trying state lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to try state lock: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> unlockStateLock(String lockKey, String channelName, String responseCacheKey, String requestUuid, String ownerId, String newState, String stateKeySuffix, String stateExpirationMs, String responseCacheTtl) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                
                // Check if lock exists and is owned by the requester
                String currentOwner = commands.get(lockKey);
                if (currentOwner == null) {
                    // Lock doesn't exist
                    return false;
                }
                
                if (!currentOwner.equals(ownerId)) {
                    // Lock is held by a different owner
                    return false;
                }
                
                // Delete the lock
                commands.del(lockKey);
                
                // Update state if needed
                if (newState != null && stateKeySuffix != null) {
                    String stateKey = lockKey + stateKeySuffix;
                    commands.set(stateKey, newState);
                    
                    // Set expiration for state if provided
                    if (stateExpirationMs != null && !stateExpirationMs.isEmpty()) {
                        try {
                            long expirationMs = Long.parseLong(stateExpirationMs);
                            if (expirationMs > 0) {
                                commands.pexpire(stateKey, expirationMs);
                            }
                        } catch (NumberFormatException e) {
                            log.warn("Invalid state expiration value: {}", stateExpirationMs);
                        }
                    }
                }
                
                // Publish unlock message if channel name is provided
                if (channelName != null && !channelName.isEmpty()) {
                    commands.publish(channelName, "UNLOCKED");
                }
                
                return true;
            } catch (Exception e) {
                log.error("Error unlocking state lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to unlock state lock: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<String> getStateLockState(String stateKey) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                return commands.get(stateKey);
            } catch (Exception e) {
                log.error("Error getting state lock state: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to get state lock state: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Long> tryWriteLock(String lockKey, String requestUuid, String responseCacheKey, String lockTtl, String ownerId, String stampedDataKey, String stampedLockMode) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                
                // Parse TTL
                long ttlMs;
                try {
                    ttlMs = Long.parseLong(lockTtl);
                    if (ttlMs <= 0) {
                        return -1L;
                    }
                } catch (NumberFormatException e) {
                    log.error("Invalid TTL value: {}", lockTtl);
                    return -1L;
                }
                
                // Try to acquire the lock
                SetArgs setArgs = SetArgs.Builder.nx().px(ttlMs);
                String result = commands.set(lockKey, ownerId, setArgs);
                
                if ("OK".equals(result)) {
                    // Lock acquired, generate a stamp
                    long stamp = System.nanoTime();
                    
                    // Store stamp data if needed
                    if (stampedDataKey != null) {
                        commands.set(stampedDataKey, String.valueOf(stamp));
                    }
                    
                    return stamp;
                }
                
                return -1L;
            } catch (Exception e) {
                log.error("Error trying write lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to try write lock: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Long> tryReadLock(String lockKey, String requestUuid, String responseCacheKey, String lockTtl, String ownerId, String stampedDataKey) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                
                // Parse TTL
                long ttlMs;
                try {
                    ttlMs = Long.parseLong(lockTtl);
                    if (ttlMs <= 0) {
                        return -1L;
                    }
                } catch (NumberFormatException e) {
                    log.error("Invalid TTL value: {}", lockTtl);
                    return -1L;
                }
                
                // For read locks, we use a different key pattern
                String readLockKey = lockKey + ":read:" + ownerId;
                
                // Try to acquire the read lock
                SetArgs setArgs = SetArgs.Builder.nx().px(ttlMs);
                String result = commands.set(readLockKey, ownerId, setArgs);
                
                if ("OK".equals(result)) {
                    // Read lock acquired, return TTL as stamp
                    return ttlMs;
                }
                
                return -1L;
            } catch (Exception e) {
                log.error("Error trying read lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to try read lock: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> unlockWriteLock(String lockKey, String ownerId, String stamp, String unlockCommand) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                
                // Check if lock exists and is owned by the requester
                String currentOwner = commands.get(lockKey);
                if (currentOwner == null) {
                    // Lock doesn't exist
                    return false;
                }
                
                if (!currentOwner.equals(ownerId)) {
                    // Lock is held by a different owner
                    return false;
                }
                
                // Delete the lock
                commands.del(lockKey);
                
                // Publish unlock message if command is provided
                if (unlockCommand != null && !unlockCommand.isEmpty()) {
                    commands.publish(lockKey.replace(":__locks__:", ":__unlock_channels__:"), unlockCommand);
                }
                
                return true;
            } catch (Exception e) {
                log.error("Error unlocking write lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to unlock write lock: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> unlockReadLock(String lockKey, String ownerId, String stamp) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                RedisAdvancedClusterCommands<String, String> commands = connection.sync();
                
                // For read locks, we use a different key pattern
                String readLockKey = lockKey + ":read:" + ownerId;
                
                // Check if lock exists and is owned by the requester
                String currentOwner = commands.get(readLockKey);
                if (currentOwner == null) {
                    // Lock doesn't exist
                    return false;
                }
                
                if (!currentOwner.equals(ownerId)) {
                    // Lock is held by a different owner
                    return false;
                }
                
                // Delete the lock
                commands.del(readLockKey);
                
                return true;
            } catch (Exception e) {
                log.error("Error unlocking read lock: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to unlock read lock: " + e.getMessage(), e);
            }
        });
    }

    // Validation methods moved to TestRedisLockValidationUtils
}